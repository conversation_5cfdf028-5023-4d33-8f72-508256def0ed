<script setup>
import { onMounted, ref, onUnmounted } from 'vue'

// 导入组件和模块
import MapNavbar from './control/MapNavbar.vue'
// 导入地图初始化和控制功能
import { initializeMap } from './control/OverviewMap.vue'
import { handleDraw as drawHandler, deleteSelectedFeature, clearAllFeatures, stopDrawing } from './graDraw/DrawInteraction.vue'
import { handleZoom, handleMoveToWuhan, handleReset, saveInitialMapState } from './control/MapOper.vue'
// 导入样式
import '../assets/base.css'

// 1-定义外部参数
const props = defineProps({
  viewConf: { type: Object, default: () => ({}) },
  defLyrs: { type: Array, default: () => ['vec_c', 'cva_c'] } // 默认只加载天地图及其注记
})

// 2-定义地图创建完毕的事件
const emit = defineEmits(['created'])

// 地图实例和状态变量
const map = ref(null) // 使用ref使地图对象成为响应式的
const drawType = ref(null)
let initialState = null

// 处理ESC键取消绘制
function handleKeyDown(e)
{
  if (e.key === 'Escape' && map.value)
  {
    // 如果按下ESC键，停止绘制并重置鼠标样式
    stopDrawing(map.value)
    // 重置绘制类型
    drawType.value = null
  }
}

// 处理绘制完成事件
function handleDrawCompleted(e)
{
  // 绘制完成后重置绘制类型
  drawType.value = null
  console.log('绘制完成:', e.detail.type)
}

// 处理绘制停止事件（当用户手动取消绘制时）
function handleDrawStopped()
{
  // 绘制停止后重置绘制类型
  drawType.value = null
  console.log('绘制已停止')
}

// 初始化地图
onMounted(() =>
{
  try
  {
    // 初始化地图
    map.value = initializeMap('mapDom', props.viewConf, props.defLyrs)
    console.log('地图初始化完成')

    // 保存初始状态用于复位功能
    initialState = saveInitialMapState(map.value)

    // 设置全局地图对象（如果需要）
    // @ts-ignore
    window.map = map.value

    // 添加键盘事件监听器，用于ESC键取消绘制
    document.addEventListener('keydown', handleKeyDown)

    // 添加绘制完成事件监听器
    document.addEventListener('draw-completed', handleDrawCompleted)

    // 添加绘制停止事件监听器
    document.addEventListener('draw-stopped', handleDrawStopped)

    // 触发创建完毕的事件，传回地图实例对象
    emit('created', map.value)

    console.log('地图对象已传递给父组件')

    // 不再需要延迟初始化图层控件，因为图层控制功能已移至导航栏
  }
  catch (error)
  {
    console.error('地图初始化出错:', error)
  }
})

// 组件卸载时移除事件监听器
onUnmounted(() =>
{
  document.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('draw-completed', handleDrawCompleted)
  document.removeEventListener('draw-stopped', handleDrawStopped)
})

// 工具箱事件处理函数
function onDrawPoint()
{
  // 如果当前已经是绘制点模式，则不需要重新设置
  if (drawType.value === 'Point') return

  // 如果之前在其他绘制模式，先重置鼠标样式
  if (drawType.value)
    stopDrawing(map.value)

  drawType.value = 'Point'
  drawHandler(map.value, 'Point')
}

function onDrawLine()
{
  // 如果当前已经是绘制线模式，则不需要重新设置
  if (drawType.value === 'LineString') return

  // 如果之前在其他绘制模式，先重置鼠标样式
  if (drawType.value)
    stopDrawing(map.value)

  drawType.value = 'LineString'
  drawHandler(map.value, 'LineString')
}

function onDrawPolygon()
{
  // 如果当前已经是绘制多边形模式，则不需要重新设置
  if (drawType.value === 'Polygon') return

  // 如果之前在其他绘制模式，先重置鼠标样式
  if (drawType.value)
    stopDrawing(map.value)

  drawType.value = 'Polygon'
  drawHandler(map.value, 'Polygon')
}

function onDrawBezier()
{
  // 如果当前已经是绘制贝塞尔曲线模式，则不需要重新设置
  if (drawType.value === 'bezier') return

  // 如果之前在其他绘制模式，先重置鼠标样式
  if (drawType.value)
    stopDrawing(map.value)

  drawType.value = 'bezier'
  drawHandler(map.value, 'bezier')
}

function onMeasureDistance()
{
  // 如果当前已经是测距模式，则不需要重新设置
  if (drawType.value === 'distance') return

  // 如果之前在其他绘制模式，先重置鼠标样式
  if (drawType.value)
    stopDrawing(map.value)

  drawType.value = 'distance'
  drawHandler(map.value, 'distance')
}

function onMeasureArea()
{
  // 如果当前已经是测面积模式，则不需要重新设置
  if (drawType.value === 'area') return

  // 如果之前在其他绘制模式，先重置鼠标样式
  if (drawType.value)
    stopDrawing(map.value)

  drawType.value = 'area'
  drawHandler(map.value, 'area')
}

function onZoomIn()
{
  // 如果当前在绘制模式，先重置鼠标样式
  if (drawType.value)
  {
    stopDrawing(map.value)
    drawType.value = null
  }

  handleZoom(map.value, true)
}

function onZoomOut()
{
  // 如果当前在绘制模式，先重置鼠标样式
  if (drawType.value)
  {
    stopDrawing(map.value)
    drawType.value = null
  }

  handleZoom(map.value, false)
}

function onMoveToWuhanClick()
{
  // 如果当前在绘制模式，先重置鼠标样式
  if (drawType.value)
  {
    stopDrawing(map.value)
    drawType.value = null
  }

  handleMoveToWuhan(map.value)
}

function onResetClick()
{
  // 如果当前在绘制模式，先重置鼠标样式
  if (drawType.value)
  {
    stopDrawing(map.value)
    drawType.value = null
  }

  if (initialState)
    handleReset(map.value, initialState.zoom, initialState.center, initialState.rotation)
}

// 存储删除模式的状态和控制器
let deleteController = null
let isInDeleteMode = false

function onDeleteSelectedClick()
{
  // 如果已经在删除模式，则退出删除模式
  if (isInDeleteMode && deleteController)
  {
    deleteController.stopDeleting()
    deleteController = null
    isInDeleteMode = false
    return
  }

  // 如果当前在绘制模式，先重置鼠标样式
  if (drawType.value)
  {
    stopDrawing(map.value)
    drawType.value = null
  }

  // 进入删除模式
  isInDeleteMode = true
  deleteController = deleteSelectedFeature(map.value, isInDeleteMode)
}

function onClearAllClick()
{
  // 如果当前在绘制模式，先重置鼠标样式和绘制状态
  if (drawType.value)
    drawType.value = null

  // 传递地图实例，以便在清除后重置鼠标样式
  clearAllFeatures(map.value)
}
</script>

<template>
  <div>
    <!-- 导航栏放在外部，不属于地图容器 -->
    <MapNavbar
      :onDrawPoint="onDrawPoint"
      :onDrawLine="onDrawLine"
      :onDrawPolygon="onDrawPolygon"
      :onDrawBezier="onDrawBezier"
      :onMeasureDistance="onMeasureDistance"
      :onMeasureArea="onMeasureArea"
      :onZoomIn="onZoomIn"
      :onZoomOut="onZoomOut"
      :onMoveToWuhan="onMoveToWuhanClick"
      :onReset="onResetClick"
      :onDeleteSelected="onDeleteSelectedClick"
      :onClearAll="onClearAllClick"
      :map="map"
    />

    <!-- 地图容器 -->
    <div class="map-root">
      <!-- 移除了地图上的图层控制组件，只保留导航栏中的图层控件 -->
      <div id="mapDom" class="map"></div>
    </div>
  </div>
</template>

<style>
/* 样式已移至 MapStyles.css */
</style>