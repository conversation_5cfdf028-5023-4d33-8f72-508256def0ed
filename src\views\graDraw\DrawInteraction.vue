<script>
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import Draw from 'ol/interaction/Draw.js'
import { Style, Stroke, Fill, Circle as CircleStyle } from 'ol/style.js'
import Select from 'ol/interaction/Select.js'

// 存储绘图相关的状态
let drawInteraction = null
let vectorLayer = null
let selectInteraction = null

/**
 * 停止绘制并重置鼠标样式
 * @param {Object} map OpenLayers地图实例
 * @returns {void}
 */
export function stopDrawing(map)
{
  if (!map) return

  console.log('停止绘制，重置鼠标样式')

  // 移除绘制交互
  if (drawInteraction)
  {
    // 确保移除所有事件监听器
    drawInteraction.setActive(false)
    map.removeInteraction(drawInteraction)
    drawInteraction = null
    console.log('绘制交互已移除')
  }

  // 重置鼠标样式
  const mapElement = map.getTargetElement()
  if (mapElement)
  {
    mapElement.style.cursor = 'auto'
    console.log('鼠标样式已重置')
  }

  // 移除可能存在的右键事件监听器
  if (mapElement && mapElement.handleContextMenu)
  {
    mapElement.removeEventListener('contextmenu', mapElement.handleContextMenu)
    delete mapElement.handleContextMenu
    console.log('右键事件监听器已移除')
  }

  // 重置地图上所有元素的鼠标样式
  const allElements = document.querySelectorAll('.ol-viewport, .ol-layer, .ol-overlay-container')
  allElements.forEach((el) =>
  {
    el.style.cursor = 'auto'
  })
  console.log('所有元素的鼠标样式已重置')

  // 触发自定义事件，通知外部绘制已停止
  const drawStoppedEvent = new CustomEvent('draw-stopped')
  document.dispatchEvent(drawStoppedEvent)
  console.log('绘制停止事件已触发')

  // 确保绘制图层仍然可见且在最上层
  if (map && vectorLayer)
  {
    // 使用我们创建的函数确保绘制图层可见
    ensureDrawLayerVisible(map)
  }
}

/**
 * 添加绘制交互
 * @param {Object} map OpenLayers地图实例
 * @param {String} type 绘制类型
 * @returns {void}
 */
/**
 * 添加绘制交互（包括普通绘制和贝塞尔曲线绘制）
 * @param {Object} map OpenLayers地图实例
 * @param {String} type 绘制类型
 * @returns {void}
 */
function addDrawInteraction(map, type)
{
  if (!map) return

  // 移除旧的交互，确保清理干净
  if (drawInteraction)
  {
    drawInteraction.setActive(false)
    map.removeInteraction(drawInteraction)
    drawInteraction = null
  }

  // 初始化矢量图层（如果不存在）
  if (!vectorLayer)
  {
    const isBezier = type === 'bezier'
    vectorLayer = new VectorLayer({
      properties: {
        name: isBezier ? 'bezier_layer' : 'draw_layer',
        title: isBezier ? '贝塞尔曲线图层' : '绘制图层',
        category: 'draw',
        isDrawLayer: true
      },
      source: new VectorSource(),
      style: new Style({
        fill: new Fill({ color: 'rgba(0, 153, 255, 0.2)' }),
        stroke: new Stroke({ color: '#3399ff', width: 2 }),
        image: new CircleStyle({ radius: 6, fill: new Fill({ color: '#3399ff' }) })
      }),
      zIndex: 1000
    })
    map.addLayer(vectorLayer)
  }
  else
  {
    // 确保绘制图层在最上层
    vectorLayer.setZIndex(1000)
  }

  // 设置鼠标样式为十字光标，表示正在绘制
  const mapElement = map.getTargetElement()
  if (mapElement)

    mapElement.style.cursor = 'crosshair'


  // 确定绘制类型
  const drawType = type === 'bezier' ? 'LineString' :
    type === 'distance' ? 'LineString' :
      type === 'area' ? 'Polygon' : type

  // 创建绘制交互
  drawInteraction = new Draw({
    source: vectorLayer.getSource(),
    type: drawType,
    style: new Style({
      fill: new Fill({ color: 'rgba(0, 153, 255, 0.2)' }),
      stroke: new Stroke({ color: '#3399ff', width: 2, lineDash: [5, 5] }),
      image: new CircleStyle({ radius: 6, fill: new Fill({ color: '#3399ff' }) })
    })
  })

  // 添加交互到地图
  map.addInteraction(drawInteraction)

  // 监听绘制结束事件
  const drawEndListener = (evt) =>
  {
    // 贝塞尔曲线特殊处理
    if (type === 'bezier')
    {
      const feature = evt.feature
      const coords = feature.getGeometry().getCoordinates()
      if (coords.length < 3)
      {
        alert('请至少绘制3个控制点！')
        return
      }

      // 生成贝塞尔曲线点
      const bezierCoords = getBezierCurve(coords, 100)
      feature.getGeometry().setCoordinates(bezierCoords)
      feature.set('isBezier', true)
    }
    // 测量距离
    else if (type === 'distance')
    {
      const geom = evt.feature.getGeometry()
      const length = geom.getLength()
      alert('测量距离：' + length.toFixed(2) + ' 米')
    }
    // 测量面积
    else if (type === 'area')
    {
      const geom = evt.feature.getGeometry()
      const area = geom.getArea()
      alert('测量面积：' + area.toFixed(2) + ' 平方米')
    }

    // 绘制结束后停止绘制模式，重置鼠标样式
    if (map)
    {
      // 移除事件监听器，防止内存泄漏
      if (drawInteraction)

        drawInteraction.un('drawend', drawEndListener)


      // 延迟执行，确保要素已添加到图层
      setTimeout(() =>
      {
        stopDrawing(map)
        // 触发自定义事件，通知外部绘制已完成
        const drawEndEvent = new CustomEvent('draw-completed', { detail: { type } })
        document.dispatchEvent(drawEndEvent)
      }, 100)
    }
  }

  // 添加事件监听器
  drawInteraction.on('drawend', drawEndListener)
}

/**
 * 贝塞尔曲线算法
 * @param {Array} points 控制点数组
 * @param {Number} num 曲线点数量
 * @returns {Array} 贝塞尔曲线点数组
 */
function getBezierCurve(points, num)
{
  // 仅支持三阶贝塞尔
  if (points.length < 3) return points
  let result = []
  for (let t = 0; t <= 1; t += 1 / num)
  {
    let x =
      Math.pow(1 - t, 2) * points[0][0] +
      2 * t * (1 - t) * points[1][0] +
      Math.pow(t, 2) * points[2][0]
    let y =
      Math.pow(1 - t, 2) * points[0][1] +
      2 * t * (1 - t) * points[1][1] +
      Math.pow(t, 2) * points[2][1]
    if (points.length === 4)
    {
      // 三阶贝塞尔
      x =
        Math.pow(1 - t, 3) * points[0][0] +
        3 * t * Math.pow(1 - t, 2) * points[1][0] +
        3 * Math.pow(t, 2) * (1 - t) * points[2][0] +
        Math.pow(t, 3) * points[3][0]
      y =
        Math.pow(1 - t, 3) * points[0][1] +
        3 * t * Math.pow(1 - t, 2) * points[1][1] +
        3 * Math.pow(t, 2) * (1 - t) * points[2][1] +
        Math.pow(t, 3) * points[3][1]
    }
    result.push([x, y])
  }
  return result
}

/**
 * 添加选择交互
 * @param {Object} map OpenLayers地图实例
 * @returns {void}
 */
export function addSelectInteraction(map)
{
  if (!map) return
  if (selectInteraction)
  {
    map.removeInteraction(selectInteraction)
    selectInteraction = null
  }
  selectInteraction = new Select({
    layers: [vectorLayer]
  })
  map.addInteraction(selectInteraction)
}

/**
 * 删除选中的要素
 * @param {Object} map OpenLayers地图实例
 * @param {Boolean} isActive 是否已经处于删除模式
 * @returns {Object} 包含停止删除功能的对象
 */
export function deleteSelectedFeature(map, isActive = false)
{
  if (!map || !vectorLayer)
  {
    return { stopDeleting: () =>
    {} }
  }

  // 如果已经处于删除模式，则不需要重新创建交互
  if (isActive && selectInteraction)

    return { stopDeleting: stopDeleteMode }


  // 退出绘制模式（移除绘制交互）
  if (drawInteraction)
  {
    map.removeInteraction(drawInteraction)
    drawInteraction = null
  }

  // 移除已有的选择交互
  if (selectInteraction)
  {
    map.removeInteraction(selectInteraction)
    selectInteraction = null
  }

  // 创建一个提示元素，显示当前处于删除模式
  const deleteModeTip = document.createElement('div')
  deleteModeTip.className = 'delete-mode-tip'
  deleteModeTip.innerHTML = '删除模式：点击要素进行删除，按ESC键或点击空白处退出'
  deleteModeTip.style.position = 'absolute'
  deleteModeTip.style.top = '70px'
  deleteModeTip.style.right = '20px'
  deleteModeTip.style.backgroundColor = 'rgba(255, 0, 0, 0.7)'
  deleteModeTip.style.color = 'white'
  deleteModeTip.style.padding = '8px 12px'
  deleteModeTip.style.borderRadius = '4px'
  deleteModeTip.style.zIndex = '1000'
  deleteModeTip.style.pointerEvents = 'none'
  document.body.appendChild(deleteModeTip)

  // 激活选择交互，点击要素即删除
  selectInteraction = new Select({ layers: [vectorLayer] })
  map.addInteraction(selectInteraction)

  // 监听选择事件，删除选中的要素
  const selectListener = function(e)
  {
    const selected = e.target.getFeatures()
    if (selected.getLength() > 0)
    {
      selected.forEach((f) => vectorLayer.getSource().removeFeature(f))
      selected.clear()
    }
  }

  selectInteraction.on('select', selectListener)

  // 监听键盘ESC键，退出删除模式
  const keyListener = function(e)
  {
    if (e.key === 'Escape')

      stopDeleteMode()

  }

  document.addEventListener('keydown', keyListener)

  // 监听地图点击事件，如果点击在空白处，退出删除模式
  const mapClickListener = function(e)
  {
    // 检查是否点击在要素上
    const feature = map.forEachFeatureAtPixel(e.pixel, (feature) => feature)
    if (!feature)
    {
      // 点击在空白处，退出删除模式
      stopDeleteMode()
    }
  }

  map.on('click', mapClickListener)

  // 停止删除模式的函数
  function stopDeleteMode()
  {
    if (selectInteraction)
    {
      selectInteraction.un('select', selectListener)
      map.removeInteraction(selectInteraction)
      selectInteraction = null
    }

    document.removeEventListener('keydown', keyListener)
    map.un('click', mapClickListener)

    // 移除提示元素
    if (deleteModeTip && deleteModeTip.parentNode)

      deleteModeTip.parentNode.removeChild(deleteModeTip)

  }

  // 返回停止删除的函数，以便外部调用
  return { stopDeleting: stopDeleteMode }
}

/**
 * 清除所有要素，包括绘制的图形和定位标记
 * @param {Object} map OpenLayers地图实例（可选）
 * @returns {void}
 */

export function clearAllFeatures(map = null)
{
  // 清除绘制的图形
  if (vectorLayer)

    vectorLayer.getSource().clear()


  // 清除定位标记
  if (window.customLocationControl && window.customLocationControl.clearLocationMarkers)

    window.customLocationControl.clearLocationMarkers()


  // 如果提供了地图实例，重置鼠标样式
  if (map)
  {
    stopDrawing(map)

    // 确保绘制图层仍然可见且在最上层
    if (vectorLayer)

      ensureDrawLayerVisible(map)

  }
}

/**
 * 处理绘制操作
 * @param {Object} map OpenLayers地图实例
 * @param {String} type 绘制类型
 * @returns {void}
 */
export function handleDraw(map, type)
{
  if (!map) return


  // 先移除任何现有的绘制交互，确保鼠标样式重置
  if (drawInteraction)


    stopDrawing(map)


  // 添加右键取消绘制的事件监听
  const mapElement = map.getTargetElement()
  if (mapElement)
  {
    // 定义右键点击事件处理函数
    const handleContextMenu = function(e)
    {
      // 阻止默认的右键菜单
      e.preventDefault()

      // 如果正在绘制，则取消绘制并重置鼠标样式
      if (drawInteraction)


        stopDrawing(map)

    }

    // 移除之前可能存在的右键事件监听器
    mapElement.removeEventListener('contextmenu', handleContextMenu)

    // 添加新的右键事件监听器
    mapElement.addEventListener('contextmenu', handleContextMenu)

    // 保存事件处理函数的引用，以便在stopDrawing中可以移除
    mapElement.handleContextMenu = handleContextMenu

  }

  // 添加绘制交互
  addDrawInteraction(map, type)

  // 确保绘制图层在地图上并可见
  if (map && vectorLayer)
  {
    // 使用我们创建的函数确保绘制图层可见
    ensureDrawLayerVisible(map)
  }


}
</script>

<script setup>
import MapComponent from '../Map.vue'
// 直接使用上面定义的 handleDraw 函数

const props = defineProps({
  drawType: {
    type: String,
    default: 'Point'
  }
})

/**
 * 确保绘制图层在地图上可见
 * @param {Object} map OpenLayers地图实例
 * @param {Boolean} forceAdd 是否强制添加图层，即使它已经存在
 * @returns {void}
 */
function ensureDrawLayerVisible(map, forceAdd = false)
{
  if (!map || !vectorLayer) return

  // 检查绘制图层是否在地图上
  const layers = map.getLayers().getArray()
  const drawLayerExists = layers.some(layer => layer === vectorLayer)

  // 如果绘制图层不在地图上或强制添加，重新添加它
  if (!drawLayerExists || forceAdd)
  {
    // 如果已存在且强制添加，先移除
    if (drawLayerExists && forceAdd)

      map.removeLayer(vectorLayer)


    map.addLayer(vectorLayer)
  }

  // 确保绘制图层可见且在最上层
  vectorLayer.setVisible(true)
  vectorLayer.setZIndex(1000)

  // 检查是否存在中国河海地图图层
  const hasWaterLayers = layers.some(layer =>
  {
    const name = layer.get('name')
    return name === 'water_areas' || name === 'water_lines'
  })

  // 如果存在中国河海地图图层，确保绘制图层在最上层
  if (hasWaterLayers)
  {
    // 设置更高的Z-index
    vectorLayer.setZIndex(2000)
  }
}

/**
 * 地图创建完成后的处理函数
 * @param {Object} map OpenLayers地图实例
 */
const onMapCreated = (map) =>
{
  // 根据传入的绘制类型初始化绘制交互
  handleDraw(map, props.drawType)

  // 添加地图图层变化事件监听器
  map.getLayers().on('add', (event) =>
  {
    // 当地图添加新图层时，确保绘制图层在最上层
    setTimeout(() =>
    {
      // 检查是否添加的是中国河海地图图层
      const layer = event.element
      const name = layer.get('name')
      if (name === 'water_areas' || name === 'water_lines')

        ensureDrawLayerVisible(map, true) // 强制重新添加绘制图层

      else

        ensureDrawLayerVisible(map)

    }, 100)
  })

  map.getLayers().on('remove', () =>
  {
    // 当地图移除图层时，确保绘制图层仍然存在
    setTimeout(() => ensureDrawLayerVisible(map), 100)
  })

  // 监听中国河海地图加载完成事件
  document.addEventListener('chinaWaterMapLoaded', () =>
  {
    setTimeout(() => ensureDrawLayerVisible(map, true), 200) // 强制重新添加绘制图层
  })
}
</script>

<template>
  <MapComponent @created="onMapCreated"></MapComponent>
</template>

<style>
/* 删除模式提示样式 */
.delete-mode-tip {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}
</style>
