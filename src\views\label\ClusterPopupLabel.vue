<script setup>
import { ref } from 'vue'
import Map from '../Map.vue'
import GeoJSON from 'ol/format/GeoJSON.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import { Fill, Stroke, Circle, Text, Style } from 'ol/style.js'
import Cluster from 'ol/source/Cluster'
import Overlay from 'ol/Overlay.js'

// 气泡相关的引用
const overlayDlg = ref(null)
const popupContent = ref(null)
const popupCloser = ref(null)
let popup = null

// 创建样式函数，根据聚合数量动态设置样式
const styleFunction = (feature) =>
{
  const size = feature.get('features').length
  
  // 单个点和聚合点使用不同的样式
  if (size === 1)
  {
    // 单个点的样式
    return new Style({
      image: new Circle({
        radius: 8,
        fill: new Fill({ color: 'rgba(255, 0, 0, 0.8)' }),
        stroke: new Stroke({ color: '#ffcc33', width: 2 })
      }),
      text: new Text({
        text: feature.get('features')[0].get('mc'),
        font: '12px Calibri,sans-serif',
        offsetY: -15,
        fill: new Fill({ color: '#000' }),
        stroke: new Stroke({ color: '#fff', width: 3 })
      })
    })
  }
  else
  {
    // 聚合点的样式
    return new Style({
      image: new Circle({
        radius: 10 + Math.min(size, 20), // 根据聚合数量动态调整大小，但设置上限
        fill: new Fill({ color: 'rgba(0, 0, 255, 0.6)' }),
        stroke: new Stroke({ color: '#fff', width: 2 })
      }),
      text: new Text({
        text: size.toString(),
        font: 'bold 12px Arial',
        fill: new Fill({ color: '#fff' })
      })
    })
  }
}

// 创建聚合标注
const createClusterLayer = () =>
{
  // 创建矢量数据源，加载GeoJSON数据
  const source = new VectorSource({
    url: 'data/地名.json',
    format: new GeoJSON()
  })

  // 创建聚合数据源
  const clusterSource = new Cluster({
    distance: 40, // 聚合距离
    minDistance: 20, // 最小距离
    source: source
  })

  // 创建矢量图层
  return new VectorLayer({
    source: clusterSource,
    style: styleFunction
  })
}

// 处理点击事件，显示气泡
const handleMapClick = (map, evt) =>
{
  // 获取点击位置的要素
  const feature = map.forEachFeatureAtPixel(evt.pixel, feature => feature)
  
  if (!feature)
  {
    // 如果没有点击到要素，关闭气泡
    popup.setPosition(undefined)
    return
  }

  // 获取聚合的要素
  const features = feature.get('features')
  if (!features || features.length === 0) return

  // 获取点击位置的坐标
  const coordinate = evt.coordinate

  if (features.length === 1)
  {
    // 单个点，显示其属性信息
    const pointFeature = features[0]
    const properties = pointFeature.getProperties()
    
    // 设置气泡内容
    popupContent.value.innerHTML = `<div class="popup-title">${properties.mc || '未命名位置'}</div>`
    
    // 设置气泡位置并显示
    popup.setPosition(coordinate)
  }
  else
  {
    // 聚合点，显示聚合内的所有点信息
    let content = `<div class="popup-title">包含 ${features.length} 个位置</div><ul class="popup-list">`
    
    // 最多显示5个，避免气泡过大
    const maxDisplay = Math.min(features.length, 5)
    for (let i = 0; i < maxDisplay; i++)
    {
      const properties = features[i].getProperties()
      content += `<li>${properties.mc || '未命名位置'}</li>`
    }
    
    // 如果有更多点，显示省略号
    if (features.length > maxDisplay)
    {
      content += `<li>... 等${features.length - maxDisplay}个位置</li>`
      return
    }
    
    content += '</ul>'
    
    // 设置气泡内容
    popupContent.value.innerHTML = content
    
    // 设置气泡位置并显示
    popup.setPosition(coordinate)
  }
}

// 关闭气泡
const onClose = () =>
{
  popup.setPosition(undefined)
  popupCloser.value.blur()
  return false
}

// 地图创建完成后的回调
const onClusterPopupLabelCreate = (map) =>
{
  // 创建气泡覆盖物
  popup = new Overlay({
    element: overlayDlg.value,
    positioning: 'bottom-center',
    autoPan: true,
    autoPanAnimation: {
      duration: 250
    },
    autoPanMargin: 20,
    offset: [0, -10]
  })
  
  // 将气泡添加到地图
  map.addOverlay(popup)
  
  // 创建并添加聚合图层
  const clusterLayer = createClusterLayer()
  map.addLayer(clusterLayer)
  
  // 添加点击事件监听
  map.on('click', (evt) => handleMapClick(map, evt))
  
  // 添加鼠标悬停样式变化
  map.on('pointermove', (evt) =>
  {
    const pixel = map.getEventPixel(evt.originalEvent)
    const hit = map.hasFeatureAtPixel(pixel)
    map.getTargetElement().style.cursor = hit ? 'pointer' : ''
  })
}
</script>

<template>
  <Map @created="onClusterPopupLabelCreate"></Map>
  
  <!-- 气泡标注 -->
  <div ref="overlayDlg" class="popup">
    <a href="#" ref="popupCloser" class="popup-closer" @click="onClose"></a>
    <div ref="popupContent" class="popup-content"></div>
  </div>
</template>

<style>
.popup {
  position: absolute;
  background-color: white;
  box-shadow: 0 1px 4px rgba(0,0,0,0.2);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #cccccc;
  bottom: 12px;
  left: -50px;
  min-width: 180px;
  max-width: 300px;
}

.popup-closer {
  text-decoration: none;
  position: absolute;
  top: 2px;
  right: 8px;
  color: #999;
  font-size: 16px;
}

.popup-closer:after {
  content: "✖";
}

.popup-closer:hover {
  color: #333;
}

.popup-content {
  margin-top: 5px;
  max-height: 200px;
  overflow-y: auto;
}

.popup-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #3399ff;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.popup-list {
  margin: 0;
  padding-left: 20px;
}

.popup-list li {
  margin-bottom: 3px;
}
</style>
