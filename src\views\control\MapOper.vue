<script>
/**
 * 缩放控制 - 导出函数，供Map.vue使用
 * @param {Object} map OpenLayers地图实例
 * @param {Boolean} isZoomIn 是否放大
 * @returns {void}
 */
export function handleZoom(map, isZoomIn)
{
  if (!map) return
  const view = map.getView()
  // 获取当前级别
  const curZoom = view.getZoom()
  // 放大增加一级，缩小减小一级
  view.setZoom(isZoomIn ? curZoom + 1 : curZoom - 1)
}

/**
 * 移动到武汉 - 导出函数，供Map.vue使用
 * @param {Object} map OpenLayers地图实例
 * @returns {void}
 */
export function handleMoveToWuhan(map)
{
  if (!map) return
  const view = map.getView()
  // 武汉中心
  view.setCenter([12727037.16, 3579063.69])
  view.setZoom(11)
}

/**
 * 复位地图 - 导出函数，供Map.vue使用
 * @param {Object} map OpenLayers地图实例
 * @param {Number} initialZoom 初始缩放级别
 * @param {Array} initialCenter 初始中心点
 * @param {Number} initialRotation 初始旋转角度
 * @returns {void}
 */
export function handleReset(map, initialZoom, initialCenter, initialRotation)
{
  if (!map) return
  const view = map.getView()
  view.setZoom(initialZoom)
  view.setCenter(initialCenter)
  view.setRotation(initialRotation)
}

/**
 * 保存地图初始状态 - 导出函数，供Map.vue使用
 * @param {Object} map OpenLayers地图实例
 * @returns {Object} 地图初始状态
 */
export function saveInitialMapState(map)
{
  if (!map) return null
  const view = map.getView()
  return {
    zoom: view.getZoom(),
    center: view.getCenter(),
    rotation: view.getRotation()
  }
}
</script>

<script setup>
// 这个组件现在使用Map.vue中集成的地图控件功能
// 为了保持向后兼容性，我们保留了这个文件，但功能已经移至Map.vue中的地图控件下拉菜单

import Map from '../Map.vue'

let view = null
const createControl = map =>
{
  view = map.getView()
}

// 缩放控制
const onZoom = isZoomIn =>
{
  if (!view) return

  // 获取当前级别
  const curZoom = view.getZoom()
  // 放大增加一级，缩小减小一级
  view.setZoom(isZoomIn ? curZoom + 1 : curZoom - 1)
}

// 移动到武汉
const onMoveWh = () =>
{
  if (!view) return

  // 武汉中心
  view.setCenter([12727037.16, 3579063.69])
  view.setZoom(11)
}

// 复位 - 使用Map.vue中的初始状态
const onRestore = () =>
{
  if (!view) return

  // 这里我们简单地重新加载页面，让Map.vue处理复位逻辑
  location.reload()
}
</script>

<template>
  <Map @created="createControl"></Map>
  <div class="control">
    <el-button @click="onZoom(true)">放大一级</el-button>
    <el-button @click="onZoom(false)">缩小一级</el-button>
    <el-button @click="onMoveWh()">移动到武汉</el-button>
    <el-button @click="onRestore()">复位</el-button>
  </div>
</template>

<style>
.control {position: absolute;left:80px;top:10px;}
</style>
