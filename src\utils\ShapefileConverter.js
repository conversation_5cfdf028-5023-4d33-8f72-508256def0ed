import shp from 'shpjs'

/**
 * 将Shapefile文件转换为GeoJSON格式
 * @param {string} shpPath - Shapefile文件路径
 * @returns {Promise<Object>} - 返回GeoJSON对象
 */
export async function convertShapefileToGeoJSON(shpPath) {
  try {
    console.log(`开始加载Shapefile文件: ${shpPath}`)

    // 加载shapefile文件
    const response = await fetch(shpPath)
    if (!response.ok) throw new Error(`无法加载Shapefile文件: ${response.statusText}`)

    console.log(`Shapefile文件加载成功，开始转换为ArrayBuffer`)

    // 将文件转换为ArrayBuffer
    const arrayBuffer = await response.arrayBuffer()

    console.log(`ArrayBuffer创建成功，大小: ${arrayBuffer.byteLength} 字节`)
    console.log(`开始使用shpjs转换为GeoJSON`)

    // 使用shpjs将ArrayBuffer转换为GeoJSON
    const geojson = await shp(arrayBuffer)

    console.log(
      `GeoJSON转换成功，包含 ${geojson.features ? geojson.features.length : '未知数量'} 个要素`
    )

    // 如果返回的是数组（多个GeoJSON对象），则取第一个
    if (Array.isArray(geojson)) {
      console.log(`返回了数组，包含 ${geojson.length} 个GeoJSON对象，使用第一个`)
      return geojson[0]
    }

    return geojson
  } catch (error) {
    console.error('转换Shapefile文件出错:', error)

    // 根据文件路径判断是水域面还是水系线
    const isWaterAreas = shpPath.includes('water_areas')

    // 如果是中国河海地图的数据，返回一个空的GeoJSON对象
    if (shpPath.includes('中国河海矢量数据')) {
      console.log('返回空的GeoJSON对象，不显示预设图形')
      return {
        type: 'FeatureCollection',
        features: []
      }
    }
    // 否则返回一个有效的GeoJSON对象，但不包含任何要素
    else {
      console.log('返回有效的GeoJSON对象，但不包含任何要素')
      return {
        type: 'FeatureCollection',
        features: []
      }
    }
  }
}

/**
 * 获取中国主要河流和湖泊的信息
 * @returns {Object} - 返回中国主要河流和湖泊的信息
 */
export function getWaterBodiesInfo() {
  return {
    长江: {
      type: '河流',
      description:
        '长江是中国第一大河，也是世界第三长河，全长约6,300公里。发源于青藏高原的唐古拉山脉，流经11个省市自治区，最终注入东海。长江流域面积约180万平方公里，是中国重要的经济带和生态屏障。',
      location: [104.41, 30.89], // 大致位置，实际应根据数据调整
      zoom: 5
    },
    黄河: {
      type: '河流',
      description:
        '黄河是中国第二大河，也是中华文明的发源地之一，全长约5,464公里。发源于青海省巴颜喀拉山脉，流经9个省区，注入渤海。黄河因其水中含有大量黄土而得名，是世界上含沙量最高的河流之一。',
      location: [100.14, 35.58],
      zoom: 5
    },
    嘉陵江: {
      type: '河流',
      description:
        '嘉陵江是长江上游最大支流，全长约1,119公里，流域面积约16万平方公里。发源于陕西省凤县的秦岭山脉，流经陕西、甘肃、四川等省，在重庆市汇入长江。嘉陵江流域水能资源丰富，是中国重要的水运通道和生态走廊。',
      location: [106.53, 29.56],
      zoom: 7
    },
    珠江: {
      type: '河流',
      description:
        '珠江是中国南方最大的河流系统，由西江、北江、东江等水系组成，全长约2,320公里，流域面积约45.3万平方公里。珠江流域气候温暖湿润，是中国经济最发达的地区之一。',
      location: [113.27, 23.13],
      zoom: 6
    },
    淮河: {
      type: '河流',
      description:
        '淮河是中国重要的河流之一，全长约1,000公里，流域面积约27万平方公里，是南北气候的分界线。淮河流域历史上水患频繁，现已建成完善的水利工程体系。',
      location: [117.36, 33.57],
      zoom: 7
    },
    海河: {
      type: '河流',
      description:
        '海河是中国北方重要的水系，由永定河、大清河、子牙河、南运河、北运河等河流组成，流域面积约26.6万平方公里。海河流域是中国政治、文化中心所在地，也是重要的粮食产区。',
      location: [117.21, 39.13],
      zoom: 7
    },
    辽河: {
      type: '河流',
      description:
        '辽河是中国东北地区最大的河流，全长约1,345公里，流域面积约22.9万平方公里。辽河流域是中国重要的工业和农业基地，资源丰富。',
      location: [123.38, 41.81],
      zoom: 7
    },
    松花江: {
      type: '河流',
      description:
        '松花江是黑龙江的最大支流，全长约1,927公里，流域面积约55.7万平方公里。松花江流域是中国重要的商品粮基地和工业基地。',
      location: [126.53, 45.8],
      zoom: 7
    },
    黑龙江: {
      type: '河流',
      description:
        '黑龙江是中国最北的大河，也是中俄界河，全长约4,370公里，是亚洲第三长河。黑龙江流域资源丰富，是中国重要的林业、农业和能源基地。',
      location: [127.53, 50.25],
      zoom: 6
    },
    鄱阳湖: {
      type: '湖泊',
      description:
        '鄱阳湖是中国第一大淡水湖，位于江西省北部，面积约3,500平方公里，是重要的候鸟栖息地。鄱阳湖与长江相连，具有重要的调蓄功能，也是珍稀物种白鱀豚和江豚的栖息地。',
      location: [116.0, 29.71],
      zoom: 8
    },
    洞庭湖: {
      type: '湖泊',
      description:
        '洞庭湖是中国第二大淡水湖，位于湖南省北部，面积约2,820平方公里，是长江中游重要的调蓄湖泊。洞庭湖区是中国重要的鱼米之乡，也是候鸟的重要越冬地。',
      location: [112.95, 29.46],
      zoom: 8
    },
    太湖: {
      type: '湖泊',
      description:
        '太湖是中国第三大淡水湖，位于江苏省和浙江省交界处，面积约2,250平方公里，是长江三角洲重要的水源。太湖风景秀丽，是著名的旅游胜地，也是中国重要的淡水渔业基地。',
      location: [120.14, 31.17],
      zoom: 9
    },
    洪泽湖: {
      type: '湖泊',
      description:
        '洪泽湖是中国第四大淡水湖，位于江苏省西部，面积约1,576平方公里，是淮河流域重要的调蓄湖泊。洪泽湖是中国重要的淡水渔业基地，也是候鸟的重要栖息地。',
      location: [118.48, 33.27],
      zoom: 9
    },
    青海湖: {
      type: '湖泊',
      description:
        '青海湖是中国最大的内陆咸水湖，也是中国最大的湖泊，位于青海省东北部，面积约4,400平方公里，海拔3,196米。青海湖是重要的生态屏障，也是藏传佛教的圣地。',
      location: [100.13, 36.89],
      zoom: 8
    },
    巢湖: {
      type: '湖泊',
      description:
        '巢湖是中国第五大淡水湖，位于安徽省中部，面积约760平方公里。巢湖是长江流域重要的通航湖泊，也是重要的渔业基地。',
      location: [117.38, 31.62],
      zoom: 9
    },
    洱海: {
      type: '湖泊',
      description:
        '洱海位于云南省大理白族自治州，是云南省第二大淡水湖，面积约250平方公里，海拔1,972米。洱海风景秀丽，是著名的旅游胜地，也是大理地区重要的水源。',
      location: [100.19, 25.83],
      zoom: 10
    }
  }
}
