<script setup>
import { ref, computed } from 'vue'
import MapComponent from '../Map.vue'
import TileLayer from 'ol/layer/Tile.js'
import BingMaps from 'ol/source/BingMaps.js'
import TileImage from 'ol/source/TileImage.js'
import TileGrid from 'ol/tilegrid/TileGrid.js'
import baiduProjection from '../../utils/BaiduProjection.js'

/**
 * 创建百度地图
 * @returns {TileLayer} 百度地图图层
 */
function createLyrBd()
{
  // 确保百度地图投影已加载
  console.log('百度地图投影:', baiduProjection)

  // 百度地图瓦片URL - 使用与PublicMap.vue相同的URL格式
  const url = 'http://online{0-3}.map.bdimg.com/onlinelabel/?qt=tile&x={x}&y={y}&z={z}&styles=pl&udt=20191119&scaler=1&p=1'

  // 百度地图使用的分辨率
  const resolutions = []
  for (let i = 0; i < 19; i++)
    resolutions.push(Math.pow(2, 18 - i))

  // 百度地图的原点
  const origin = [0, 0]

  // 创建切片规则对象
  const tileGrid = new TileGrid({
    origin: origin,
    resolutions: resolutions,
    tileSize: 256
  })

  return new TileLayer({
    properties: {
      name: 'baidu',
      title: '百度地图',
      category: 'basemap'
    },
    visible: false,
    source: new TileImage({
      projection: 'EPSG:3857',  // 使用Web墨卡托投影，与PublicMap.vue保持一致
      tileGrid: tileGrid,
      tileUrlFunction: function(tileCoord)
      {
        if (!tileCoord) return ''

        // 使用与PublicMap.vue相同的坐标处理方式
        let tempUrl = url
        tempUrl = tempUrl.replace('{x}', tileCoord[1] < 0 ? `M${-tileCoord[1]}` : tileCoord[1])
        tempUrl = tempUrl.replace('{y}', tileCoord[2] < 0 ? `M${tileCoord[2] + 1}` : -(tileCoord[2] + 1))
        tempUrl = tempUrl.replace('{z}', tileCoord[0])

        // 范围替换
        const match = /\{(\d+)-(\d+)\}/.exec(tempUrl)
        if (match)
        {
          const delta = parseInt(match[2]) - parseInt(match[1])
          const num = Math.round(Math.random() * delta + parseInt(match[1]))
          tempUrl = tempUrl.replace(match[0], num.toString())
        }

        return tempUrl
      },
      crossOrigin: 'anonymous', // 允许跨域请求
      wrapX: false // 禁止水平循环，避免重复显示
    })
  })
}

/**
 * 创建Bing地图
 * @returns {TileLayer} Bing地图图层
 */
function createLyrBing()
{
  // Bing地图key
  const key = 'AhO9-Jd_WXvWxVMu6ynrFQbCJxl-qz-_vPLcbkPYQvjJ6fYvzxRp_wUQzP9JqXTy'
  return new TileLayer({
    properties: {
      name: 'bing',
      title: 'Bing地图',
      category: 'basemap'
    },
    visible: false,
    source: new BingMaps({
      key: key,
      imagerySet: 'RoadOnDemand',
      maxZoom: 19,
      culture: 'zh-CN', // 使用中文
      crossOrigin: 'anonymous', // 允许跨域请求
      placeholderTiles: false // 禁用占位符瓦片
    })
  })
}

// 图层数据
const layers = ref([])
const checks = ref([])
let olmap = null

// 所有图层都显示在基础地图分类下
const filteredLayers = computed(() =>
{
  return layers.value
})

// 初始化图层
const initLayers = (map) =>
{
  if (!map) return

  olmap = map

  // 清空当前图层数据
  layers.value = []
  checks.value = []

  try
  {
    // 获取当前地图中已有的图层
    const existingLayers = map.getLayers().getArray()
    const existingLayerNames = existingLayers.map(layer => layer.get('name'))

    console.log('已存在的图层:', existingLayerNames)

    // 移除所有已存在的图层（除了基础图层）
    const layersToRemove = ['baidu', 'bing']
    layersToRemove.forEach(layerName =>
    {
      if (existingLayerNames.includes(layerName))
      {
        const layersToRemove = existingLayers.filter(layer => layer.get('name') === layerName)
        layersToRemove.forEach(layer =>
        {
          map.removeLayer(layer)
          console.log(`移除旧的${layerName}图层`)
        })
      }
    })

    // 确保百度地图投影已加载
    console.log('百度地图投影:', baiduProjection)

    // 添加新的百度地图图层
    try
    {
      const baiduLayer = createLyrBd()
      map.addLayer(baiduLayer)
      console.log('添加新的百度地图图层')

      // 确保百度地图图层可见性正确设置
      baiduLayer.setVisible(false)
    }
    catch (error)
    {
      console.error('添加百度地图图层时出错:', error)
    }

    // 添加新的Bing地图图层
    try
    {
      const bingLayer = createLyrBing()
      map.addLayer(bingLayer)
      console.log('添加新的Bing地图图层')

      // 确保Bing地图图层可见性正确设置
      bingLayer.setVisible(false)
    }
    catch (error)
    {
      console.error('添加Bing地图图层时出错:', error)
    }

    console.log('所有图层已添加')
  }
  catch (error)
  {
    console.error('添加图层时出错:', error)
  }

  // 获取所有图层信息
  try
  {
    const layerArray = map.getLayers().getArray()
    console.log('获取到的图层数量:', layerArray.length)

    // 图层名称映射表，确保显示友好的名称
    const layerTitles = {
      'vec_c': '天地图',
      'cva_c': '天地图注记',
      'gaode': '高德地图',
      'baidu': '百度地图',
      'bing': 'Bing地图'
    }

    // 允许的图层名称列表（只保留有意义的图层）
    const allowedLayers = ['vec_c', 'cva_c', 'gaode', 'baidu', 'bing']

    // 处理所有图层，过滤掉不需要的图层
    layers.value = layerArray
      .filter(layer =>
      {
        const name = layer.get('name')
        return allowedLayers.includes(name)
      })
      .map(layer =>
      {
        const name = layer.get('name')
        const title = layer.get('title') || layerTitles[name] || name
        const category = layer.get('category') || 'basemap'

        // 如果图层可见，添加到选中数组
        if (layer.getVisible())
          checks.value.push(name)

        return {
          name: name,
          title: title,
          category: category,
          locate: layer.get('locate'),
          layer
        }
      })

    console.log('处理后的图层数量:', layers.value.length)
    console.log('选中的图层数量:', checks.value.length)

    // 确保至少有一个图层被选中，优先选择天地图
    if (checks.value.length === 0 && layers.value.length > 0)
    {
      const vecLayer = layers.value.find(l => l.name === 'vec_c')
      const cvaLayer = layers.value.find(l => l.name === 'cva_c')

      if (vecLayer)
      {
        checks.value.push(vecLayer.name)
        vecLayer.layer.setVisible(true)
        console.log('选择默认图层: 天地图')
      }

      if (cvaLayer)
      {
        checks.value.push(cvaLayer.name)
        cvaLayer.layer.setVisible(true)
        console.log('选择默认图层: 天地图注记')
      }

      // 如果没有天地图，则选择第一个图层
      if (!vecLayer && !cvaLayer && layers.value.length > 0)
      {
        const defaultLayer = layers.value[0]
        checks.value.push(defaultLayer.name)
        defaultLayer.layer.setVisible(true)
        console.log('选择默认图层:', defaultLayer.name)
      }
    }
  }
  catch (error)
  {
    console.error('处理图层信息时出错:', error)
  }
}

// 图层开关控制
const onCheckChange = (layerName) =>
{
  if (!olmap) return

  console.log('切换图层:', layerName, '当前选中:', checks.value)

  // 找到当前图层
  const currentLayer = layers.value.find(l => l.name === layerName)
  if (!currentLayer)
  {
    console.error('找不到图层:', layerName)
    return
  }

  // 特殊处理：天地图注记可以与其他图层同时选中
  const isTiandituAnnotation = layerName === 'cva_c'

  // 更新选中状态
  const index = checks.value.indexOf(layerName)
  if (index > -1)
  {
    // 如果已选中，则取消选中
    checks.value.splice(index, 1)
  }
  else
  {
    // 如果未选中，则选中

    // 如果是天地图注记，保留其他选中的图层
    if (isTiandituAnnotation)
    
      checks.value.push(layerName)
    
    // 如果是天地图底图，保留天地图注记，清除其他图层
    else if (layerName === 'vec_c')
    {
      // 保留天地图注记
      const hasAnnotation = checks.value.includes('cva_c')
      checks.value = hasAnnotation ? ['vec_c', 'cva_c'] : ['vec_c']
    }
    // 如果是其他图层（百度、高德、Bing等），清除所有其他图层，只保留当前图层和可能的天地图注记
    else
    {
      // 检查是否有天地图注记
      const hasAnnotation = checks.value.includes('cva_c')
      checks.value = hasAnnotation ? [layerName, 'cva_c'] : [layerName]
    }
  }

  console.log('更新后的选中状态:', checks.value)

  // 更新图层可见性
  layers.value.forEach(layer =>
  {
    const isVisible = checks.value.includes(layer.name)
    console.log(`设置图层 ${layer.name} 可见性:`, isVisible)
    layer.layer.setVisible(isVisible)
  })

  // 如果有定位信息，则移动到该位置
  if (currentLayer && currentLayer.locate && checks.value.includes(layerName))
  {
    console.log('移动到位置:', currentLayer.locate)
    olmap.getView().setZoom(currentLayer.locate[2])
    olmap.getView().setCenter([currentLayer.locate[0], currentLayer.locate[1]])
  }

  // 确保至少有一个图层被选中，优先选择天地图
  if (checks.value.length === 0 || (checks.value.length === 1 && checks.value[0] === 'cva_c'))
  {
    console.log('没有主要图层被选中，选择默认图层')

    // 优先选择天地图
    const vecLayer = layers.value.find(l => l.name === 'vec_c')

    // 检查是否已有天地图注记
    const hasAnnotation = checks.value.includes('cva_c')

    if (vecLayer)
    {
      if (hasAnnotation)
      {
        // 如果已有天地图注记，添加天地图底图
        checks.value = ['vec_c', 'cva_c']
      }
      else
      {
        // 如果没有天地图注记，只添加天地图底图
        checks.value = ['vec_c']
      }
      vecLayer.layer.setVisible(true)
      console.log('选择默认图层: 天地图')
    }
    // 如果没有天地图，则选择第一个非注记图层
    else
    {
      const nonAnnotationLayers = layers.value.filter(l => l.name !== 'cva_c')
      if (nonAnnotationLayers.length > 0)
      {
        const defaultLayer = nonAnnotationLayers[0]

        if (hasAnnotation)
        {
          // 如果已有天地图注记，添加默认图层
          checks.value = [defaultLayer.name, 'cva_c']
        }
        else
        {
          // 如果没有天地图注记，只添加默认图层
          checks.value = [defaultLayer.name]
        }

        defaultLayer.layer.setVisible(true)
        console.log('选择默认图层:', defaultLayer.name)
      }
    }

    // 更新所有图层可见性
    layers.value.forEach(layer =>
    {
      const isVisible = checks.value.includes(layer.name)
      layer.layer.setVisible(isVisible)
    })
  }
}

// 组件挂载后初始化图层
const createLayerManage = map =>
{
  initLayers(map)
}

// 暴露方法和属性给父组件
defineExpose({
  initLayers,
  onCheckChange,
  layers,
  checks
})
</script>

<template>
  <MapComponent :def-lyrs="['vec_c','cva_c']" @created="createLayerManage"></MapComponent>
  <div class="layer-switcher">
    <div class="layer-switcher-panel">
      <div class="layer-switcher-title">图层控制</div>

      <div class="layer-switcher-content">
        <!-- 显示图层数量 -->
        <div class="category-info" v-if="filteredLayers.length > 0">
          基础地图 ({{ filteredLayers.length }})
        </div>

        <!-- 无数据提示 -->
        <div class="no-layers" v-if="filteredLayers.length === 0">
          <span class="no-data-icon">📭</span>
          <span class="no-data-text">该分类下暂无图层</span>
        </div>

        <!-- 图层列表 -->
        <div class="layer-checkbox-group" v-else>
          <div
            v-for="layer in filteredLayers"
            :key="layer.name"
            class="layer-checkbox-item"
            @click="onCheckChange(layer.name)"
          >
            <div class="checkbox-wrapper">
              <div class="checkbox" :class="{ 'checked': checks.includes(layer.name) }">
                <div class="checkbox-inner" v-if="checks.includes(layer.name)">✓</div>
              </div>
              <div class="checkbox-label">{{ layer.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.layer-switcher {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.layer-switcher-panel {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  width: 240px;
}

.layer-switcher-title {
  background: linear-gradient(to right, #3399ff, #2277dd);
  color: white;
  padding: 10px 15px;
  font-weight: bold;
  font-size: 16px;
  text-align: center;
}

/* 分类信息样式 */
.category-info {
  font-size: 13px;
  color: #666;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dashed #eee;
}

/* 无数据提示样式 */
.no-layers {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #999;
}

.no-data-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.no-data-text {
  font-size: 14px;
}

.layer-switcher-content {
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.layer-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-checkbox-item {
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.layer-checkbox-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  margin-right: 8px;
}

.checkbox.checked {
  border-color: #3399ff;
  background-color: #3399ff;
}

.checkbox-inner {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  font-size: 14px;
  color: #333;
}
</style>
